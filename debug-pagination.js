import FirecrawlApp from '@mendable/firecrawl-js';
import fs from 'fs-extra';
import dotenv from 'dotenv';

dotenv.config();

class PaginationDebugger {
    constructor() {
        this.apiKey = process.env.FIRECRAWL_API_KEY;
        this.firecrawl = new FirecrawlApp({ apiKey: this.apiKey });
        this.baseUrl = 'https://img.hyun.cc';
        this.categoryPath = '/index.php/category/mn/';
    }

    async debugPagination() {
        console.log('🔍 调试美女分类分页结构...');
        
        // 测试不同的分页URL格式
        const urlFormats = [
            // 格式1: /page/2/
            (page) => `${this.baseUrl}${this.categoryPath}page/${page}/`,
            
            // 格式2: ?page=2
            (page) => `${this.baseUrl}${this.categoryPath}?page=${page}`,
            
            // 格式3: /2.html
            (page) => `${this.baseUrl}${this.categoryPath}${page}.html`,
            
            // 格式4: /2/
            (page) => `${this.baseUrl}${this.categoryPath}${page}/`,
        ];
        
        for (let formatIndex = 0; formatIndex < urlFormats.length; formatIndex++) {
            console.log(`\n📄 测试URL格式 ${formatIndex + 1}:`);
            
            for (let page = 1; page <= 5; page++) {
                const url = urlFormats[formatIndex](page);
                console.log(`🔗 测试: ${url}`);
                
                try {
                    const result = await this.firecrawl.scrapeUrl(url, {
                        formats: ['html'],
                        waitFor: 2000
                    });

                    if (result.success) {
                        const articles = this.extractArticles(result.html);
                        console.log(`✅ 第${page}页: ${articles.length} 个图集`);
                        
                        if (articles.length > 0) {
                            console.log(`   前3个图集:`);
                            articles.slice(0, 3).forEach((article, index) => {
                                console.log(`   ${index + 1}. ID:${article.id} - ${article.url}`);
                            });
                        }
                        
                        // 如果找到了有效的格式，继续测试更多页
                        if (articles.length > 0 && page === 1) {
                            console.log(`🎯 格式 ${formatIndex + 1} 有效，继续测试更多页...`);
                            await this.testMorePages(urlFormats[formatIndex]);
                            return; // 找到有效格式后退出
                        }
                    } else {
                        console.log(`❌ 第${page}页失败: ${result.error || '未知错误'}`);
                    }
                    
                    await this.delay(1000);
                    
                } catch (error) {
                    console.log(`❌ 第${page}页异常: ${error.message}`);
                }
                
                if (page === 1 && formatIndex > 0) {
                    // 如果第1页都失败，跳过这个格式
                    break;
                }
            }
        }
    }

    async testMorePages(urlFormat) {
        console.log('\n🔍 测试更多页面...');
        
        for (let page = 1; page <= 10; page++) {
            const url = urlFormat(page);
            
            try {
                const result = await this.firecrawl.scrapeUrl(url, {
                    formats: ['html'],
                    waitFor: 2000
                });

                if (result.success) {
                    const articles = this.extractArticles(result.html);
                    console.log(`📄 第${page}页: ${articles.length} 个图集`);
                    
                    if (articles.length === 0) {
                        console.log(`📄 第${page}页无内容，可能已到末页`);
                        if (page > 1) {
                            console.log(`✅ 美女分类总共有 ${page - 1} 页`);
                        }
                        break;
                    }
                    
                    // 保存第一页的详细信息
                    if (page === 1) {
                        await fs.writeFile('debug-page1.html', result.html);
                        console.log('💾 第1页HTML已保存到 debug-page1.html');
                    }
                    
                } else {
                    console.log(`❌ 第${page}页失败: ${result.error || '未知错误'}`);
                    break;
                }
                
                await this.delay(2000);
                
            } catch (error) {
                console.log(`❌ 第${page}页异常: ${error.message}`);
                break;
            }
        }
    }

    extractArticles(html) {
        const articles = [];
        const linkRegex = /href="([^"]*\/archives\/(\d+)\.html)"/g;
        let match;
        
        while ((match = linkRegex.exec(html)) !== null) {
            const fullUrl = match[1];
            const articleId = match[2];
            const articleUrl = fullUrl.startsWith('http') ? fullUrl : `${this.baseUrl}${fullUrl}`;
            
            articles.push({
                id: articleId,
                url: articleUrl
            });
        }
        
        // 去重
        const seen = new Set();
        return articles.filter(article => {
            if (seen.has(article.id)) return false;
            seen.add(article.id);
            return true;
        });
    }

    delay(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    async analyzePaginationStructure() {
        console.log('\n🔍 分析分页结构...');
        
        try {
            const firstPageUrl = `${this.baseUrl}${this.categoryPath}`;
            const result = await this.firecrawl.scrapeUrl(firstPageUrl, {
                formats: ['html'],
                waitFor: 2000
            });

            if (result.success) {
                await fs.writeFile('debug-pagination.html', result.html);
                console.log('💾 分页HTML已保存到 debug-pagination.html');
                
                // 查找分页相关的HTML结构
                this.findPaginationElements(result.html);
            }
            
        } catch (error) {
            console.error('❌ 分析分页结构失败:', error.message);
        }
    }

    findPaginationElements(html) {
        console.log('\n🔍 查找分页元素...');
        
        // 查找常见的分页模式
        const paginationPatterns = [
            // 数字分页
            /<a[^>]*href="([^"]*)"[^>]*>(\d+)<\/a>/g,
            
            // 下一页链接
            /<a[^>]*href="([^"]*)"[^>]*>(?:下一页|next|Next|>|»)/gi,
            
            // 分页容器
            /<div[^>]*class="[^"]*(?:page|pagination|pager)[^"]*"[^>]*>([\s\S]*?)<\/div>/gi,
            /<nav[^>]*class="[^"]*(?:page|pagination|pager)[^"]*"[^>]*>([\s\S]*?)<\/nav>/gi,
            
            // 页码链接
            /page\/(\d+)/g,
            /\?page=(\d+)/g,
        ];
        
        paginationPatterns.forEach((pattern, index) => {
            const matches = [...html.matchAll(pattern)];
            if (matches.length > 0) {
                console.log(`📄 模式 ${index + 1} 找到 ${matches.length} 个匹配:`);
                matches.slice(0, 5).forEach((match, i) => {
                    console.log(`   ${i + 1}. ${match[0].substring(0, 100)}...`);
                });
            }
        });
        
        // 查找总页数信息
        const totalPagesPatterns = [
            /共\s*(\d+)\s*页/,
            /total[^>]*(\d+)[^>]*pages?/i,
            /(\d+)\s*\/\s*(\d+)/,
        ];
        
        totalPagesPatterns.forEach((pattern, index) => {
            const match = html.match(pattern);
            if (match) {
                console.log(`📊 总页数模式 ${index + 1}: ${match[0]}`);
            }
        });
    }
}

// 主程序
async function main() {
    const debugger = new PaginationDebugger();
    
    console.log('🔍 开始调试美女分类分页...');
    
    // 先分析分页结构
    await debugger.analyzePaginationStructure();
    
    // 然后测试不同的URL格式
    await debugger.debugPagination();
}

main().catch(console.error);
