import FirecrawlApp from '@mendable/firecrawl-js';
import fs from 'fs-extra';
import path from 'path';
import axios from 'axios';
import dotenv from 'dotenv';

dotenv.config();

class CompleteBeautyScraper {
    constructor() {
        this.apiKey = process.env.FIRECRAWL_API_KEY;
        this.firecrawl = new FirecrawlApp({ apiKey: this.apiKey });
        this.baseUrl = 'https://img.hyun.cc';
        this.outputDir = './downloads/美女';
        
        this.categoryPath = '/index.php/category/mn/';
        this.categoryName = '美女';
        
        this.stats = {
            totalImages: 0,
            totalArticles: 0,
            failedDownloads: 0,
            skippedArticles: 0,
            startTime: Date.now()
        };
        
        // 保守但完整的配置
        this.concurrentLimit = 2;
        this.imageDownloadConcurrency = 5;
        this.maxPages = 50;                 // 大幅增加最大页数
        this.pageDelay = 3000;              // 增加页面延迟
        this.taskDelay = 2000;
        this.imageDelay = 100;
        this.retryDelay = 5000;
        this.maxRetries = 3;
        
        // 多种分页URL格式
        this.paginationFormats = [
            (page) => page === 1 ? `${this.baseUrl}${this.categoryPath}` : `${this.baseUrl}${this.categoryPath}page/${page}/`,
            (page) => page === 1 ? `${this.baseUrl}${this.categoryPath}` : `${this.baseUrl}${this.categoryPath}?page=${page}`,
            (page) => page === 1 ? `${this.baseUrl}${this.categoryPath}` : `${this.baseUrl}${this.categoryPath}${page}/`,
        ];
    }

    async init() {
        if (!this.apiKey) {
            throw new Error('请设置 FIRECRAWL_API_KEY 环境变量');
        }
        
        await fs.ensureDir(this.outputDir);
        
        console.log('💃 美女分类完整爬虫初始化完成');
        console.log(`📁 输出目录: ${this.outputDir}`);
        console.log(`⚡ 配置: 文章${this.concurrentLimit}个 | 图片${this.imageDownloadConcurrency}个`);
        console.log(`📄 最大翻页: ${this.maxPages}页`);
        console.log(`🔄 多种分页格式测试`);
    }

    async scrapeCompleteBeautyCategory() {
        console.log('💃 开始完整爬取美女分类...');
        
        // 首先确定正确的分页格式
        const workingFormat = await this.findWorkingPaginationFormat();
        
        if (!workingFormat) {
            console.log('❌ 无法确定分页格式，使用默认格式');
            const articles = await this.getAllBeautyArticlesWithFormat(this.paginationFormats[0]);
            await this.processAllArticles(articles);
            return;
        }
        
        console.log('✅ 找到有效的分页格式');
        
        // 使用正确的格式获取所有文章
        const articles = await this.getAllBeautyArticlesWithFormat(workingFormat);
        
        if (articles.length === 0) {
            console.log('❌ 未找到任何美女图集');
            return;
        }
        
        await this.processAllArticles(articles);
    }

    async findWorkingPaginationFormat() {
        console.log('\n🔍 测试分页格式...');
        
        for (let formatIndex = 0; formatIndex < this.paginationFormats.length; formatIndex++) {
            const format = this.paginationFormats[formatIndex];
            console.log(`📄 测试格式 ${formatIndex + 1}...`);
            
            try {
                // 测试第1页
                const page1Url = format(1);
                const page1Result = await this.firecrawlWithRetry(page1Url, {
                    formats: ['html'],
                    waitFor: 2000
                });
                
                if (!page1Result.success) {
                    console.log(`❌ 格式 ${formatIndex + 1} 第1页失败`);
                    continue;
                }
                
                const page1Articles = this.extractArticles(page1Result.html);
                console.log(`📄 格式 ${formatIndex + 1} 第1页: ${page1Articles.length} 个图集`);
                
                if (page1Articles.length === 0) {
                    console.log(`❌ 格式 ${formatIndex + 1} 第1页无内容`);
                    continue;
                }
                
                // 测试第2页
                const page2Url = format(2);
                const page2Result = await this.firecrawlWithRetry(page2Url, {
                    formats: ['html'],
                    waitFor: 2000
                });
                
                if (page2Result.success) {
                    const page2Articles = this.extractArticles(page2Result.html);
                    console.log(`📄 格式 ${formatIndex + 1} 第2页: ${page2Articles.length} 个图集`);
                    
                    // 如果第2页有不同的内容，说明分页有效
                    if (page2Articles.length > 0) {
                        const page1Ids = new Set(page1Articles.map(a => a.id));
                        const page2Ids = new Set(page2Articles.map(a => a.id));
                        const hasNewContent = [...page2Ids].some(id => !page1Ids.has(id));
                        
                        if (hasNewContent) {
                            console.log(`✅ 格式 ${formatIndex + 1} 有效 - 第2页有新内容`);
                            return format;
                        }
                    }
                }
                
                // 如果第2页无内容，可能只有1页，也算有效
                console.log(`⚠️  格式 ${formatIndex + 1} 可能只有1页`);
                return format;
                
            } catch (error) {
                console.log(`❌ 格式 ${formatIndex + 1} 测试失败: ${error.message}`);
            }
            
            await this.delay(2000);
        }
        
        return null;
    }

    async getAllBeautyArticlesWithFormat(urlFormat) {
        const articles = [];
        let consecutiveEmptyPages = 0;
        const maxEmptyPages = 5; // 增加容错
        
        console.log(`\n📋 使用确定的格式收集所有图集...`);
        
        for (let page = 1; page <= this.maxPages; page++) {
            try {
                const categoryUrl = urlFormat(page);
                console.log(`📄 爬取第 ${page} 页: ${categoryUrl}`);
                
                const result = await this.firecrawlWithRetry(categoryUrl, {
                    formats: ['html'],
                    waitFor: 2000
                });

                if (!result.success) {
                    console.log(`⚠️  第${page}页爬取失败`);
                    consecutiveEmptyPages++;
                    if (consecutiveEmptyPages >= maxEmptyPages) {
                        console.log(`❌ 连续${maxEmptyPages}页失败，停止翻页`);
                        break;
                    }
                    continue;
                }

                const pageArticles = this.extractArticles(result.html);
                
                if (pageArticles.length === 0) {
                    consecutiveEmptyPages++;
                    console.log(`📄 第${page}页无内容 (连续空页: ${consecutiveEmptyPages})`);
                    
                    if (consecutiveEmptyPages >= maxEmptyPages) {
                        console.log(`📄 连续${maxEmptyPages}页无内容，停止翻页`);
                        break;
                    }
                } else {
                    consecutiveEmptyPages = 0;
                    
                    // 检查是否有新内容
                    const existingIds = new Set(articles.map(a => a.id));
                    const newArticles = pageArticles.filter(a => !existingIds.has(a.id));
                    
                    if (newArticles.length === 0 && page > 1) {
                        console.log(`📄 第${page}页无新内容，可能已到末页`);
                        break;
                    }
                    
                    articles.push(...newArticles);
                    console.log(`📄 第${page}页: +${newArticles.length} 个新图集 (总计: ${articles.length})`);
                }
                
                await this.delay(this.pageDelay);
                
            } catch (error) {
                console.error(`❌ 第${page}页出错: ${error.message}`);
                consecutiveEmptyPages++;
                if (consecutiveEmptyPages >= maxEmptyPages) {
                    break;
                }
                await this.delay(this.retryDelay);
            }
        }
        
        const uniqueArticles = this.removeDuplicates(articles);
        console.log(`\n✅ 收集完成: ${uniqueArticles.length} 个唯一图集`);
        
        return uniqueArticles;
    }

    async firecrawlWithRetry(url, options, retryCount = 0) {
        try {
            const result = await this.firecrawl.scrapeUrl(url, options);
            return result;
        } catch (error) {
            if (retryCount < this.maxRetries && (error.message.includes('429') || error.message.includes('500'))) {
                console.log(`🔄 重试 ${retryCount + 1}/${this.maxRetries}: ${url}`);
                await this.delay(this.retryDelay * (retryCount + 1));
                return this.firecrawlWithRetry(url, options, retryCount + 1);
            }
            throw error;
        }
    }

    extractArticles(html) {
        const articles = [];
        const linkRegex = /href="([^"]*\/archives\/(\d+)\.html)"/g;
        let match;
        
        while ((match = linkRegex.exec(html)) !== null) {
            const fullUrl = match[1];
            const articleId = match[2];
            const articleUrl = fullUrl.startsWith('http') ? fullUrl : `${this.baseUrl}${fullUrl}`;
            
            articles.push({
                id: articleId,
                title: `图集_${articleId}`,
                url: articleUrl
            });
        }
        
        return articles;
    }

    removeDuplicates(articles) {
        const seen = new Set();
        return articles.filter(article => {
            if (seen.has(article.id)) return false;
            seen.add(article.id);
            return true;
        });
    }

    async processAllArticles(articles) {
        console.log(`\n📊 美女分类完整统计:`);
        console.log(`📸 总图集数: ${articles.length} 个`);
        console.log(`🐌 开始处理...`);
        
        const chunks = this.chunkArray(articles, this.concurrentLimit);
        let processedCount = 0;
        
        for (let chunkIndex = 0; chunkIndex < chunks.length; chunkIndex++) {
            const chunk = chunks[chunkIndex];
            const startTime = Date.now();
            
            console.log(`\n🐌 处理块 ${chunkIndex + 1}/${chunks.length} (${chunk.length} 个图集)`);
            
            const promises = chunk.map((article, index) => 
                this.processArticleWithRetry(article, processedCount + index)
            );
            
            const results = await Promise.allSettled(promises);
            
            let chunkSuccess = 0;
            let chunkFailed = 0;
            let chunkSkipped = 0;
            
            results.forEach((result, index) => {
                if (result.status === 'fulfilled') {
                    if (result.value === 'success') {
                        chunkSuccess++;
                        this.stats.totalArticles++;
                    } else if (result.value === 'skipped') {
                        chunkSkipped++;
                        this.stats.skippedArticles++;
                    } else {
                        chunkFailed++;
                        this.stats.failedDownloads++;
                    }
                } else {
                    chunkFailed++;
                    this.stats.failedDownloads++;
                }
            });
            
            const chunkTime = Math.round((Date.now() - startTime) / 1000);
            processedCount += chunk.length;
            const progress = ((processedCount / articles.length) * 100).toFixed(1);
            
            console.log(`🐌 块${chunkIndex + 1}完成: ✅${chunkSuccess} ⏭️${chunkSkipped} ❌${chunkFailed} | ${chunkTime}s | 进度${processedCount}/${articles.length} (${progress}%)`);
            console.log(`📊 当前统计: 图集${this.stats.totalArticles}个 | 图片${this.stats.totalImages}张`);
            
            if (chunkIndex < chunks.length - 1) {
                await this.delay(this.taskDelay);
            }
        }
    }

    async processArticleWithRetry(article, taskIndex) {
        for (let retry = 0; retry <= this.maxRetries; retry++) {
            try {
                return await this.processArticle(article, taskIndex);
            } catch (error) {
                if (retry < this.maxRetries && (error.message.includes('429') || error.message.includes('500'))) {
                    console.log(`🔄 [${taskIndex}] 重试 ${retry + 1}/${this.maxRetries}: ${article.title}`);
                    await this.delay(this.retryDelay * (retry + 1));
                } else {
                    console.error(`❌ [${taskIndex}] 最终失败: ${error.message}`);
                    return 'failed';
                }
            }
        }
        return 'failed';
    }

    async processArticle(article, taskIndex) {
        console.log(`💃 [${taskIndex}] ${article.url}`);
        
        const result = await this.firecrawlWithRetry(article.url, {
            formats: ['html'],
            waitFor: 2500,
            onlyMainContent: false
        });

        if (!result.success) {
            console.log(`❌ [${taskIndex}] 爬取失败`);
            return 'failed';
        }

        const realTitle = this.extractPostTitle(result.html);
        const finalTitle = realTitle || article.title;
        
        const cleanTitle = this.sanitizeFilename(finalTitle);
        const articleDir = path.join(this.outputDir, cleanTitle);
        
        if (await fs.pathExists(articleDir)) {
            const existingFiles = await fs.readdir(articleDir);
            const imageFiles = existingFiles.filter(file => /\.(jpg|jpeg|png|gif|webp)$/i.test(file));
            if (imageFiles.length > 0) {
                console.log(`⏭️  [${taskIndex}] ${cleanTitle}: 已存在 (${imageFiles.length}张图片)`);
                return 'skipped';
            }
        }
        
        const imageUrls = this.extractImagesFromMainContent(result.html);
        
        if (imageUrls.length === 0) {
            console.log(`⚠️  [${taskIndex}] ${finalTitle}: 无图片`);
            return 'failed';
        }

        await fs.ensureDir(articleDir);
        
        const downloadResults = await this.downloadImagesConservatively(imageUrls, articleDir);
        
        console.log(`✅ [${taskIndex}] ${cleanTitle}: ${downloadResults.success}/${imageUrls.length} 张图片`);
        this.stats.totalImages += downloadResults.success;
        
        return downloadResults.success > 0 ? 'success' : 'failed';
    }

    extractPostTitle(html) {
        const titlePatterns = [
            /<h1[^>]*class="[^"]*posttitle[^"]*"[^>]*>(.*?)<\/h1>/is,
            /<h1[^>]*class="[^"]*title[^"]*"[^>]*>(.*?)<\/h1>/is,
            /<h1[^>]*>(.*?)<\/h1>/is
        ];
        
        for (const pattern of titlePatterns) {
            const match = html.match(pattern);
            if (match && match[1]) {
                let text = match[1].replace(/<[^>]*>/g, '').trim();
                text = text.replace(/「\d+」\s*$/, '').trim();
                text = text.replace(/\s+/g, ' ').trim();
                
                if (text.length > 3 && text.length < 100) {
                    return text;
                }
            }
        }
        return null;
    }

    extractImagesFromMainContent(html) {
        const contentPatterns = [
            /<article[^>]*>([\s\S]*?)<\/article>/i,
            /<div[^>]*class="[^"]*content[^"]*"[^>]*>([\s\S]*?)<\/div>/i,
            /<main[^>]*>([\s\S]*?)<\/main>/i
        ];

        let mainContent = null;
        
        for (const pattern of contentPatterns) {
            const match = html.match(pattern);
            if (match) {
                mainContent = match[1];
                break;
            }
        }
        
        if (!mainContent) {
            mainContent = this.cleanHtmlForImageExtraction(html);
        }
        
        const imageUrls = [];
        const nsaPattern = /https?:\/\/t\.nsa\.cc\/d\/[A-Za-z0-9_-]+/g;
        let match;
        
        while ((match = nsaPattern.exec(mainContent)) !== null) {
            imageUrls.push(match[0]);
        }
        
        return [...new Set(imageUrls)];
    }

    cleanHtmlForImageExtraction(html) {
        const removePatterns = [
            /<nav[^>]*>[\s\S]*?<\/nav>/gi,
            /<header[^>]*>[\s\S]*?<\/header>/gi,
            /<footer[^>]*>[\s\S]*?<\/footer>/gi,
            /<aside[^>]*>[\s\S]*?<\/aside>/gi,
            /<div[^>]*class="[^"]*(?:sidebar|widget|recommend|related|nav|menu)[^"]*"[^>]*>[\s\S]*?<\/div>/gi
        ];
        
        let cleaned = html;
        for (const pattern of removePatterns) {
            cleaned = cleaned.replace(pattern, '');
        }
        
        return cleaned;
    }

    async downloadImagesConservatively(imageUrls, outputDir) {
        let success = 0;
        let failed = 0;
        
        const chunks = this.chunkArray(imageUrls, this.imageDownloadConcurrency);
        
        for (const chunk of chunks) {
            const promises = chunk.map((url, localIndex) => {
                const globalIndex = imageUrls.indexOf(url) + 1;
                return this.downloadImage(url, outputDir, globalIndex);
            });
            
            const results = await Promise.allSettled(promises);
            
            results.forEach(result => {
                if (result.status === 'fulfilled' && result.value) {
                    success++;
                } else {
                    failed++;
                }
            });
            
            if (chunks.indexOf(chunk) < chunks.length - 1) {
                await this.delay(this.imageDelay);
            }
        }
        
        return { success, failed };
    }

    async downloadImage(imageUrl, outputDir, index) {
        const filename = `${String(index).padStart(2, '0')}.jpg`;
        const filePath = path.join(outputDir, filename);
        
        if (await fs.pathExists(filePath)) {
            try {
                const stats = await fs.stat(filePath);
                if (stats.size > 1024) return true;
                await fs.unlink(filePath);
            } catch (error) {
                // 忽略文件检查错误
            }
        }
        
        try {
            const response = await axios({
                method: 'GET',
                url: imageUrl,
                responseType: 'stream',
                timeout: 20000,
                headers: {
                    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
                    'Referer': this.baseUrl,
                    'Connection': 'keep-alive'
                }
            });
            
            const writer = fs.createWriteStream(filePath);
            response.data.pipe(writer);
            
            await new Promise((resolve, reject) => {
                writer.on('finish', resolve);
                writer.on('error', reject);
                setTimeout(() => reject(new Error('写入超时')), 25000);
            });
            
            try {
                const stats = await fs.stat(filePath);
                if (stats.size < 1024) {
                    await fs.unlink(filePath);
                    return false;
                }
                return true;
            } catch (error) {
                return false;
            }
            
        } catch (error) {
            try {
                if (await fs.pathExists(filePath)) {
                    await fs.unlink(filePath);
                }
            } catch (cleanupError) {
                // 忽略清理错误
            }
            return false;
        }
    }

    sanitizeFilename(filename) {
        if (!filename) return '未命名图集';
        
        let cleaned = filename.replace(/[<>:"/\\|?*]/g, '');
        cleaned = cleaned.replace(/\s+/g, ' ').trim();
        
        if (cleaned.length > 50) {
            const breakPoints = [' - ', '（', '(', ' ', '-'];
            let truncated = cleaned.substring(0, 50);
            
            for (const breakPoint of breakPoints) {
                const lastIndex = truncated.lastIndexOf(breakPoint);
                if (lastIndex > 15) {
                    truncated = truncated.substring(0, lastIndex);
                    break;
                }
            }
            cleaned = truncated;
        }
        
        return cleaned || '未命名图集';
    }

    chunkArray(array, size) {
        const chunks = [];
        for (let i = 0; i < array.length; i += size) {
            chunks.push(array.slice(i, i + size));
        }
        return chunks;
    }

    delay(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    showStats() {
        const elapsed = Math.round((Date.now() - this.stats.startTime) / 1000);
        const hours = Math.floor(elapsed / 3600);
        const minutes = Math.floor((elapsed % 3600) / 60);
        const seconds = elapsed % 60;
        
        const avgTimePerArticle = this.stats.totalArticles > 0 ? (elapsed / this.stats.totalArticles).toFixed(1) : 0;
        const avgImagesPerArticle = this.stats.totalArticles > 0 ? (this.stats.totalImages / this.stats.totalArticles).toFixed(1) : 0;
        
        console.log('\n💃 美女分类完整爬取完成！');
        console.log('='.repeat(60));
        console.log(`⏱️  总用时: ${hours}h ${minutes}m ${seconds}s`);
        console.log(`📁 成功图集: ${this.stats.totalArticles} 个`);
        console.log(`⏭️  跳过图集: ${this.stats.skippedArticles} 个 (已存在)`);
        console.log(`❌ 失败图集: ${this.stats.failedDownloads} 个`);
        console.log(`🖼️  总图片: ${this.stats.totalImages} 张`);
        console.log(`⚡ 平均速度: ${avgTimePerArticle}s/图集 | ${avgImagesPerArticle}张/图集`);
        console.log(`📂 保存位置: ${this.outputDir}`);
        console.log('='.repeat(60));
    }

    async run() {
        console.log('💃 Hyun Gallery 美女分类完整爬虫');
        console.log('===================================');
        
        try {
            await this.init();
            await this.scrapeCompleteBeautyCategory();
            this.showStats();
            
        } catch (error) {
            console.error('❌ 运行出错:', error.message);
        }
    }
}

// 主程序
async function main() {
    const scraper = new CompleteBeautyScraper();
    await scraper.run();
}

main().catch(console.error);
