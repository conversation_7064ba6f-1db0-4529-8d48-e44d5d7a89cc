import fs from 'fs-extra';
import path from 'path';

class RecommendationImageRemover {
    constructor() {
        this.baseDir = './downloads/美女';
        this.stats = {
            totalFolders: 0,
            processedFolders: 0,
            removedImages: 0,
            skippedFolders: 0,
            errors: 0
        };
    }

    async removeRecommendationImages() {
        console.log('🗑️  开始删除每个图集的最后两张推荐图片...');
        console.log(`📁 扫描目录: ${this.baseDir}`);
        
        try {
            // 检查目录是否存在
            if (!await fs.pathExists(this.baseDir)) {
                console.log('❌ 美女目录不存在');
                return;
            }

            // 获取所有图集文件夹
            const folders = await fs.readdir(this.baseDir);
            this.stats.totalFolders = folders.length;
            
            console.log(`📊 找到 ${folders.length} 个图集文件夹`);
            
            // 处理每个图集文件夹
            for (let i = 0; i < folders.length; i++) {
                const folderName = folders[i];
                const folderPath = path.join(this.baseDir, folderName);
                
                try {
                    // 检查是否为目录
                    const stat = await fs.stat(folderPath);
                    if (!stat.isDirectory()) {
                        console.log(`⏭️  跳过文件: ${folderName}`);
                        this.stats.skippedFolders++;
                        continue;
                    }
                    
                    await this.processFolder(folderPath, folderName, i + 1);
                    this.stats.processedFolders++;
                    
                } catch (error) {
                    console.error(`❌ 处理文件夹 ${folderName} 出错: ${error.message}`);
                    this.stats.errors++;
                }
            }
            
            this.showSummary();
            
        } catch (error) {
            console.error('❌ 扫描目录出错:', error.message);
        }
    }

    async processFolder(folderPath, folderName, index) {
        console.log(`\n📁 [${index}] 处理: ${folderName}`);
        
        try {
            // 获取文件夹中的所有文件
            const files = await fs.readdir(folderPath);
            
            // 过滤出图片文件并排序
            const imageFiles = files
                .filter(file => /\.(jpg|jpeg|png|gif|webp)$/i.test(file))
                .sort((a, b) => {
                    // 按文件名数字排序
                    const numA = parseInt(a.match(/\d+/)?.[0] || '0');
                    const numB = parseInt(b.match(/\d+/)?.[0] || '0');
                    return numA - numB;
                });
            
            console.log(`   📸 找到 ${imageFiles.length} 张图片`);
            
            if (imageFiles.length <= 2) {
                console.log(`   ⚠️  图片数量 ≤ 2，跳过删除`);
                return;
            }
            
            // 获取最后两张图片
            const lastTwoImages = imageFiles.slice(-2);
            console.log(`   🗑️  准备删除最后两张: ${lastTwoImages.join(', ')}`);
            
            // 删除最后两张图片
            let removedCount = 0;
            for (const imageFile of lastTwoImages) {
                const imagePath = path.join(folderPath, imageFile);
                
                try {
                    await fs.unlink(imagePath);
                    console.log(`   ✅ 已删除: ${imageFile}`);
                    removedCount++;
                    this.stats.removedImages++;
                } catch (error) {
                    console.error(`   ❌ 删除失败 ${imageFile}: ${error.message}`);
                }
            }
            
            console.log(`   📊 ${folderName}: 删除了 ${removedCount}/2 张推荐图片，剩余 ${imageFiles.length - removedCount} 张`);
            
        } catch (error) {
            console.error(`   ❌ 读取文件夹内容出错: ${error.message}`);
            throw error;
        }
    }

    showSummary() {
        console.log('\n🎉 推荐图片删除完成！');
        console.log('='.repeat(50));
        console.log(`📁 总文件夹数: ${this.stats.totalFolders}`);
        console.log(`✅ 处理成功: ${this.stats.processedFolders}`);
        console.log(`⏭️  跳过文件夹: ${this.stats.skippedFolders}`);
        console.log(`❌ 处理错误: ${this.stats.errors}`);
        console.log(`🗑️  删除图片数: ${this.stats.removedImages}`);
        console.log(`📊 平均每个图集删除: ${(this.stats.removedImages / Math.max(this.stats.processedFolders, 1)).toFixed(1)} 张`);
        console.log('='.repeat(50));
    }

    // 预览模式：只显示会删除什么，不实际删除
    async previewRemoval() {
        console.log('👀 预览模式：查看将要删除的推荐图片...');
        console.log(`📁 扫描目录: ${this.baseDir}`);
        
        try {
            if (!await fs.pathExists(this.baseDir)) {
                console.log('❌ 美女目录不存在');
                return;
            }

            const folders = await fs.readdir(this.baseDir);
            console.log(`📊 找到 ${folders.length} 个图集文件夹`);
            
            let totalWillRemove = 0;
            
            for (let i = 0; i < Math.min(folders.length, 10); i++) { // 只预览前10个
                const folderName = folders[i];
                const folderPath = path.join(this.baseDir, folderName);
                
                try {
                    const stat = await fs.stat(folderPath);
                    if (!stat.isDirectory()) continue;
                    
                    const files = await fs.readdir(folderPath);
                    const imageFiles = files
                        .filter(file => /\.(jpg|jpeg|png|gif|webp)$/i.test(file))
                        .sort((a, b) => {
                            const numA = parseInt(a.match(/\d+/)?.[0] || '0');
                            const numB = parseInt(b.match(/\d+/)?.[0] || '0');
                            return numA - numB;
                        });
                    
                    if (imageFiles.length > 2) {
                        const lastTwoImages = imageFiles.slice(-2);
                        console.log(`📁 ${folderName} (${imageFiles.length}张):`);
                        console.log(`   🗑️  将删除: ${lastTwoImages.join(', ')}`);
                        totalWillRemove += 2;
                    }
                    
                } catch (error) {
                    console.error(`❌ 预览 ${folderName} 出错: ${error.message}`);
                }
            }
            
            if (folders.length > 10) {
                console.log(`\n... 还有 ${folders.length - 10} 个文件夹未显示`);
            }
            
            const estimatedTotal = Math.round((totalWillRemove / Math.min(folders.length, 10)) * folders.length);
            console.log(`\n📊 预计总共删除约 ${estimatedTotal} 张推荐图片`);
            
        } catch (error) {
            console.error('❌ 预览出错:', error.message);
        }
    }

    // 安全模式：备份最后两张图片到单独文件夹
    async safeRemoval() {
        console.log('🛡️  安全模式：备份推荐图片后删除...');
        
        const backupDir = './backup-recommendations';
        await fs.ensureDir(backupDir);
        
        try {
            if (!await fs.pathExists(this.baseDir)) {
                console.log('❌ 美女目录不存在');
                return;
            }

            const folders = await fs.readdir(this.baseDir);
            this.stats.totalFolders = folders.length;
            
            console.log(`📊 找到 ${folders.length} 个图集文件夹`);
            console.log(`💾 备份目录: ${backupDir}`);
            
            for (let i = 0; i < folders.length; i++) {
                const folderName = folders[i];
                const folderPath = path.join(this.baseDir, folderName);
                
                try {
                    const stat = await fs.stat(folderPath);
                    if (!stat.isDirectory()) {
                        this.stats.skippedFolders++;
                        continue;
                    }
                    
                    await this.safeProcessFolder(folderPath, folderName, backupDir, i + 1);
                    this.stats.processedFolders++;
                    
                } catch (error) {
                    console.error(`❌ 安全处理文件夹 ${folderName} 出错: ${error.message}`);
                    this.stats.errors++;
                }
            }
            
            this.showSummary();
            console.log(`💾 备份位置: ${backupDir}`);
            
        } catch (error) {
            console.error('❌ 安全删除出错:', error.message);
        }
    }

    async safeProcessFolder(folderPath, folderName, backupDir, index) {
        console.log(`\n📁 [${index}] 安全处理: ${folderName}`);
        
        try {
            const files = await fs.readdir(folderPath);
            const imageFiles = files
                .filter(file => /\.(jpg|jpeg|png|gif|webp)$/i.test(file))
                .sort((a, b) => {
                    const numA = parseInt(a.match(/\d+/)?.[0] || '0');
                    const numB = parseInt(b.match(/\d+/)?.[0] || '0');
                    return numA - numB;
                });
            
            console.log(`   📸 找到 ${imageFiles.length} 张图片`);
            
            if (imageFiles.length <= 2) {
                console.log(`   ⚠️  图片数量 ≤ 2，跳过处理`);
                return;
            }
            
            // 创建备份文件夹
            const folderBackupDir = path.join(backupDir, folderName);
            await fs.ensureDir(folderBackupDir);
            
            const lastTwoImages = imageFiles.slice(-2);
            console.log(`   💾 备份并删除最后两张: ${lastTwoImages.join(', ')}`);
            
            let processedCount = 0;
            for (const imageFile of lastTwoImages) {
                const sourcePath = path.join(folderPath, imageFile);
                const backupPath = path.join(folderBackupDir, imageFile);
                
                try {
                    // 先备份
                    await fs.copy(sourcePath, backupPath);
                    console.log(`   💾 已备份: ${imageFile}`);
                    
                    // 再删除
                    await fs.unlink(sourcePath);
                    console.log(`   🗑️  已删除: ${imageFile}`);
                    
                    processedCount++;
                    this.stats.removedImages++;
                } catch (error) {
                    console.error(`   ❌ 处理失败 ${imageFile}: ${error.message}`);
                }
            }
            
            console.log(`   📊 ${folderName}: 备份并删除了 ${processedCount}/2 张推荐图片`);
            
        } catch (error) {
            console.error(`   ❌ 安全处理文件夹内容出错: ${error.message}`);
            throw error;
        }
    }
}

// 主程序
async function main() {
    const remover = new RecommendationImageRemover();
    
    // 获取命令行参数
    const args = process.argv.slice(2);
    const mode = args[0] || 'preview';
    
    console.log('🗑️  推荐图片删除工具');
    console.log('==================');
    
    switch (mode) {
        case 'preview':
            console.log('👀 运行预览模式...');
            await remover.previewRemoval();
            console.log('\n💡 如需实际删除，请运行: node remove-recommendation-images.js remove');
            console.log('💡 如需安全删除（备份），请运行: node remove-recommendation-images.js safe');
            break;
            
        case 'remove':
            console.log('🗑️  运行删除模式...');
            await remover.removeRecommendationImages();
            break;
            
        case 'safe':
            console.log('🛡️  运行安全删除模式...');
            await remover.safeRemoval();
            break;
            
        default:
            console.log('❌ 无效模式。可用模式:');
            console.log('   preview - 预览将要删除的文件');
            console.log('   remove  - 直接删除推荐图片');
            console.log('   safe    - 备份后删除推荐图片');
            break;
    }
}

main().catch(console.error);
