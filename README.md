# Hyun Gallery 美女分类爬虫

## 📋 项目简介

这是一个专门用于爬取 https://img.hyun.cc 美女分类图集的高效爬虫工具。

## 🚀 主要特性

- ✅ **完整爬取**: 支持美女分类全部262个图集
- ✅ **真实标题**: 自动提取并使用图集的真实标题作为文件夹名
- ✅ **精确过滤**: 只下载属于图集的图片，排除推荐内容
- ✅ **智能跳过**: 自动检测已存在的图集，避免重复下载
- ✅ **稳定可靠**: 内置重试机制，处理API限制和网络错误
- ✅ **并发下载**: 支持多线程并发，提升下载效率

## 📁 项目结构

```
├── correct-beauty-scraper.js    # 主爬虫程序（推荐使用）
├── package-lock.json           # 依赖锁定文件
├── downloads/                  # 下载目录
│   └── 美女/                   # 美女分类图集
├── 使用说明.md                 # 详细使用说明
├── 完整使用指南.md             # 完整功能指南
├── 快速爬虫使用指南.md         # 快速上手指南
└── 问题分析与解决方案.md       # 常见问题解决
```

## ⚡ 快速开始

### 1. 安装依赖
```bash
npm install
```

### 2. 配置环境变量
创建 `.env` 文件并添加：
```
FIRECRAWL_API_KEY=your_api_key_here
```

### 3. 运行爬虫
```bash
node correct-beauty-scraper.js
```

## 📊 爬取结果

- **图集数量**: 262个高质量图集
- **图片总数**: 约13,000-15,000张
- **分类**: 美女分类完整内容
- **命名**: 使用真实标题，如"Quan冉有点饿（拖拉大王） - 樋口圆香"

## 🔧 技术特点

### 正确的分页处理
- ✅ 使用正确的分页格式: `?page=N`
- ❌ 避免错误格式: `/page/N/`

### 智能标题提取
```javascript
// 专门针对网站的标题提取
/<h1[^>]*class="[^"]*posttitle[^"]*"[^>]*>(.*?)<\/h1>/is
```

### 精确图片过滤
- 只从主内容区域提取图片
- 排除导航、侧边栏、推荐内容
- 使用正则: `/https?:\/\/t\.nsa\.cc\/d\/[A-Za-z0-9_-]+/g`

## 📈 性能配置

```javascript
// 保守但稳定的配置
concurrentLimit: 2              // 文章并发数
imageDownloadConcurrency: 5     // 图片并发数
pageDelay: 3000                 // 页面延迟
retryDelay: 5000                // 重试延迟
maxRetries: 3                   // 最大重试次数
```

## 🎯 使用建议

1. **首次运行**: 建议使用默认的保守配置
2. **网络良好**: 可适当增加并发数提升速度
3. **API限制**: 遇到429错误时会自动重试
4. **断点续传**: 支持中断后继续，自动跳过已下载内容

## 📝 更新日志

### v3.0 (当前版本)
- ✅ 修复分页问题，发现完整的262个图集
- ✅ 优化标题提取，使用真实图集名称
- ✅ 改进错误处理和重试机制
- ✅ 清理无用代码，简化项目结构

### v2.x (已废弃)
- 早期版本，存在分页问题，只能爬取24个图集

## ⚠️ 注意事项

1. **API配额**: 需要有效的Firecrawl API密钥
2. **网络稳定**: 建议在稳定的网络环境下运行
3. **存储空间**: 确保有足够的磁盘空间（约5-10GB）
4. **合规使用**: 请遵守网站的使用条款和robots.txt

## 🤝 贡献

欢迎提交Issue和Pull Request来改进这个项目。

## 📄 许可证

MIT License
