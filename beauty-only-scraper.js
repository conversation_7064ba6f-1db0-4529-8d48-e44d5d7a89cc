import FirecrawlApp from '@mendable/firecrawl-js';
import fs from 'fs-extra';
import path from 'path';
import axios from 'axios';
import dotenv from 'dotenv';

dotenv.config();

class BeautyOnlyScraper {
    constructor() {
        this.apiKey = process.env.FIRECRAWL_API_KEY;
        this.firecrawl = new FirecrawlApp({ apiKey: this.apiKey });
        this.baseUrl = 'https://img.hyun.cc';
        this.outputDir = './downloads/美女';
        
        // 专注美女分类
        this.categoryPath = '/index.php/category/mn/';
        this.categoryName = '美女';
        
        this.stats = {
            totalImages: 0,
            totalArticles: 0,
            failedDownloads: 0,
            skippedArticles: 0,
            startTime: Date.now()
        };
        
        // 针对单分类优化的配置
        this.concurrentLimit = 6;           // 增加并发数
        this.imageDownloadConcurrency = 10; // 增加图片并发
        this.maxPages = 20;                 // 增加翻页深度
        this.pageDelay = 300;               // 进一步减少延迟
        this.taskDelay = 100;
        this.imageDelay = 30;
    }

    async init() {
        if (!this.apiKey) {
            throw new Error('请设置 FIRECRAWL_API_KEY 环境变量');
        }
        
        await fs.ensureDir(this.outputDir);
        
        console.log('💃 美女分类专用爬虫初始化完成');
        console.log(`📁 输出目录: ${this.outputDir}`);
        console.log(`⚡ 并发配置: 文章${this.concurrentLimit}个 | 图片${this.imageDownloadConcurrency}个`);
        console.log(`📄 最大翻页: ${this.maxPages}页`);
        console.log(`⏱️  延迟配置: 页面${this.pageDelay}ms | 任务${this.taskDelay}ms | 图片${this.imageDelay}ms`);
    }

    async scrapeBeautyCategory() {
        console.log('💃 开始爬取美女分类全部图集...');
        
        // 获取所有美女分类的文章
        const articles = await this.getAllBeautyArticles();
        
        if (articles.length === 0) {
            console.log('❌ 未找到任何美女图集');
            return;
        }
        
        console.log(`\n📊 美女分类统计:`);
        console.log(`📸 总图集数: ${articles.length} 个`);
        console.log(`🚀 开始高速并发处理...`);
        
        await this.processArticlesConcurrently(articles);
    }

    async getAllBeautyArticles() {
        const articles = [];
        let consecutiveEmptyPages = 0;
        const maxEmptyPages = 3; // 连续3页无内容则停止
        
        console.log(`\n📋 收集美女分类所有图集...`);
        
        for (let page = 1; page <= this.maxPages; page++) {
            try {
                const categoryUrl = page === 1 
                    ? `${this.baseUrl}${this.categoryPath}`
                    : `${this.baseUrl}${this.categoryPath}page/${page}/`;
                
                console.log(`📄 爬取第 ${page} 页...`);
                
                const result = await this.firecrawl.scrapeUrl(categoryUrl, {
                    formats: ['html'],
                    waitFor: 1200
                });

                if (!result.success) {
                    console.log(`⚠️  第${page}页爬取失败: ${result.error || '未知错误'}`);
                    consecutiveEmptyPages++;
                    if (consecutiveEmptyPages >= maxEmptyPages) {
                        console.log(`❌ 连续${maxEmptyPages}页失败，停止翻页`);
                        break;
                    }
                    continue;
                }

                const pageArticles = this.extractArticles(result.html);
                
                if (pageArticles.length === 0) {
                    consecutiveEmptyPages++;
                    console.log(`📄 第${page}页无内容 (连续空页: ${consecutiveEmptyPages})`);
                    
                    if (consecutiveEmptyPages >= maxEmptyPages) {
                        console.log(`📄 连续${maxEmptyPages}页无内容，停止翻页`);
                        break;
                    }
                } else {
                    consecutiveEmptyPages = 0; // 重置计数器
                    articles.push(...pageArticles);
                    console.log(`📄 第${page}页: +${pageArticles.length} 个图集 (总计: ${articles.length})`);
                }
                
                await this.delay(this.pageDelay);
                
            } catch (error) {
                console.error(`❌ 第${page}页出错: ${error.message}`);
                consecutiveEmptyPages++;
                if (consecutiveEmptyPages >= maxEmptyPages) {
                    break;
                }
            }
        }
        
        // 去重并返回
        const uniqueArticles = this.removeDuplicates(articles);
        console.log(`\n✅ 收集完成: ${uniqueArticles.length} 个唯一图集`);
        
        return uniqueArticles;
    }

    extractArticles(html) {
        const articles = [];
        const linkRegex = /href="([^"]*\/archives\/(\d+)\.html)"/g;
        let match;
        
        while ((match = linkRegex.exec(html)) !== null) {
            const fullUrl = match[1];
            const articleId = match[2];
            const articleUrl = fullUrl.startsWith('http') ? fullUrl : `${this.baseUrl}${fullUrl}`;
            
            articles.push({
                id: articleId,
                title: `图集_${articleId}`,
                url: articleUrl
            });
        }
        
        return articles;
    }

    removeDuplicates(articles) {
        const seen = new Set();
        return articles.filter(article => {
            if (seen.has(article.id)) return false;
            seen.add(article.id);
            return true;
        });
    }

    async processArticlesConcurrently(articles) {
        const chunks = this.chunkArray(articles, this.concurrentLimit);
        let processedCount = 0;
        
        console.log(`📦 任务分块: ${chunks.length} 个块，每块 ${this.concurrentLimit} 个并发`);
        
        for (let chunkIndex = 0; chunkIndex < chunks.length; chunkIndex++) {
            const chunk = chunks[chunkIndex];
            const startTime = Date.now();
            
            console.log(`\n🚀 处理块 ${chunkIndex + 1}/${chunks.length} (${chunk.length} 个图集)`);
            
            // 并发处理当前块
            const promises = chunk.map((article, index) => 
                this.processArticle(article, processedCount + index)
            );
            
            const results = await Promise.allSettled(promises);
            
            // 统计结果
            let chunkSuccess = 0;
            let chunkFailed = 0;
            let chunkSkipped = 0;
            
            results.forEach((result, index) => {
                if (result.status === 'fulfilled') {
                    if (result.value === 'success') {
                        chunkSuccess++;
                        this.stats.totalArticles++;
                    } else if (result.value === 'skipped') {
                        chunkSkipped++;
                        this.stats.skippedArticles++;
                    } else {
                        chunkFailed++;
                        this.stats.failedDownloads++;
                    }
                } else {
                    chunkFailed++;
                    this.stats.failedDownloads++;
                }
            });
            
            const chunkTime = Math.round((Date.now() - startTime) / 1000);
            processedCount += chunk.length;
            const progress = ((processedCount / articles.length) * 100).toFixed(1);
            
            console.log(`⚡ 块${chunkIndex + 1}完成: ✅${chunkSuccess} ⏭️${chunkSkipped} ❌${chunkFailed} | ${chunkTime}s | 进度${processedCount}/${articles.length} (${progress}%)`);
            
            // 显示实时统计
            console.log(`📊 当前统计: 图集${this.stats.totalArticles}个 | 图片${this.stats.totalImages}张`);
            
            if (chunkIndex < chunks.length - 1) {
                await this.delay(this.taskDelay);
            }
        }
    }

    async processArticle(article, taskIndex) {
        try {
            console.log(`💃 [${taskIndex}] ${article.url}`);
            
            const result = await this.firecrawl.scrapeUrl(article.url, {
                formats: ['html'],
                waitFor: 1800,
                onlyMainContent: false
            });

            if (!result.success) {
                console.log(`❌ [${taskIndex}] 爬取失败: ${result.error || '未知错误'}`);
                return 'failed';
            }

            // 提取标题
            const realTitle = this.extractPostTitle(result.html);
            const finalTitle = realTitle || article.title;
            
            // 检查是否已存在
            const cleanTitle = this.sanitizeFilename(finalTitle);
            const articleDir = path.join(this.outputDir, cleanTitle);
            
            if (await fs.pathExists(articleDir)) {
                const existingFiles = await fs.readdir(articleDir);
                const imageFiles = existingFiles.filter(file => /\.(jpg|jpeg|png|gif|webp)$/i.test(file));
                if (imageFiles.length > 0) {
                    console.log(`⏭️  [${taskIndex}] ${cleanTitle}: 已存在 (${imageFiles.length}张图片)`);
                    return 'skipped';
                }
            }
            
            // 提取图片
            const imageUrls = this.extractImagesFromMainContent(result.html);
            
            if (imageUrls.length === 0) {
                console.log(`⚠️  [${taskIndex}] ${finalTitle}: 无图片`);
                return 'failed';
            }

            // 创建目录
            await fs.ensureDir(articleDir);
            
            // 并行下载图片
            const downloadResults = await this.downloadImagesParallel(imageUrls, articleDir);
            
            console.log(`✅ [${taskIndex}] ${cleanTitle}: ${downloadResults.success}/${imageUrls.length} 张图片`);
            this.stats.totalImages += downloadResults.success;
            
            return downloadResults.success > 0 ? 'success' : 'failed';
            
        } catch (error) {
            console.error(`❌ [${taskIndex}] ${article.title}: ${error.message}`);
            return 'failed';
        }
    }

    extractPostTitle(html) {
        const titlePatterns = [
            /<h1[^>]*class="[^"]*posttitle[^"]*"[^>]*>(.*?)<\/h1>/is,
            /<h1[^>]*class="[^"]*title[^"]*"[^>]*>(.*?)<\/h1>/is,
            /<h1[^>]*>(.*?)<\/h1>/is
        ];
        
        for (const pattern of titlePatterns) {
            const match = html.match(pattern);
            if (match && match[1]) {
                let text = match[1].replace(/<[^>]*>/g, '').trim();
                text = text.replace(/「\d+」\s*$/, '').trim();
                text = text.replace(/\s+/g, ' ').trim();
                
                if (text.length > 3 && text.length < 100) {
                    return text;
                }
            }
        }
        return null;
    }

    extractImagesFromMainContent(html) {
        const contentPatterns = [
            /<article[^>]*>([\s\S]*?)<\/article>/i,
            /<div[^>]*class="[^"]*content[^"]*"[^>]*>([\s\S]*?)<\/div>/i,
            /<main[^>]*>([\s\S]*?)<\/main>/i
        ];

        let mainContent = null;
        
        for (const pattern of contentPatterns) {
            const match = html.match(pattern);
            if (match) {
                mainContent = match[1];
                break;
            }
        }
        
        if (!mainContent) {
            mainContent = this.cleanHtmlForImageExtraction(html);
        }
        
        const imageUrls = [];
        const nsaPattern = /https?:\/\/t\.nsa\.cc\/d\/[A-Za-z0-9_-]+/g;
        let match;
        
        while ((match = nsaPattern.exec(mainContent)) !== null) {
            imageUrls.push(match[0]);
        }
        
        return [...new Set(imageUrls)];
    }

    cleanHtmlForImageExtraction(html) {
        const removePatterns = [
            /<nav[^>]*>[\s\S]*?<\/nav>/gi,
            /<header[^>]*>[\s\S]*?<\/header>/gi,
            /<footer[^>]*>[\s\S]*?<\/footer>/gi,
            /<aside[^>]*>[\s\S]*?<\/aside>/gi,
            /<div[^>]*class="[^"]*(?:sidebar|widget|recommend|related|nav|menu)[^"]*"[^>]*>[\s\S]*?<\/div>/gi
        ];
        
        let cleaned = html;
        for (const pattern of removePatterns) {
            cleaned = cleaned.replace(pattern, '');
        }
        
        return cleaned;
    }

    async downloadImagesParallel(imageUrls, outputDir) {
        let success = 0;
        let failed = 0;
        
        // 将图片分批并行下载
        const chunks = this.chunkArray(imageUrls, this.imageDownloadConcurrency);
        
        for (const chunk of chunks) {
            const promises = chunk.map((url, localIndex) => {
                const globalIndex = imageUrls.indexOf(url) + 1;
                return this.downloadImage(url, outputDir, globalIndex);
            });
            
            const results = await Promise.allSettled(promises);
            
            results.forEach(result => {
                if (result.status === 'fulfilled' && result.value) {
                    success++;
                } else {
                    failed++;
                }
            });
            
            if (chunks.indexOf(chunk) < chunks.length - 1) {
                await this.delay(this.imageDelay);
            }
        }
        
        return { success, failed };
    }

    async downloadImage(imageUrl, outputDir, index) {
        const filename = `${String(index).padStart(2, '0')}.jpg`;
        const filePath = path.join(outputDir, filename);
        
        if (await fs.pathExists(filePath)) {
            try {
                const stats = await fs.stat(filePath);
                if (stats.size > 1024) return true;
                await fs.unlink(filePath);
            } catch (error) {
                // 忽略文件检查错误
            }
        }
        
        try {
            const response = await axios({
                method: 'GET',
                url: imageUrl,
                responseType: 'stream',
                timeout: 18000,
                headers: {
                    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
                    'Referer': this.baseUrl,
                    'Connection': 'keep-alive'
                }
            });
            
            const writer = fs.createWriteStream(filePath);
            response.data.pipe(writer);
            
            await new Promise((resolve, reject) => {
                writer.on('finish', resolve);
                writer.on('error', reject);
                setTimeout(() => reject(new Error('写入超时')), 22000);
            });
            
            try {
                const stats = await fs.stat(filePath);
                if (stats.size < 1024) {
                    await fs.unlink(filePath);
                    return false;
                }
                return true;
            } catch (error) {
                return false;
            }
            
        } catch (error) {
            try {
                if (await fs.pathExists(filePath)) {
                    await fs.unlink(filePath);
                }
            } catch (cleanupError) {
                // 忽略清理错误
            }
            return false;
        }
    }

    sanitizeFilename(filename) {
        if (!filename) return '未命名图集';
        
        let cleaned = filename.replace(/[<>:"/\\|?*]/g, '');
        cleaned = cleaned.replace(/\s+/g, ' ').trim();
        
        if (cleaned.length > 50) {
            const breakPoints = [' - ', '（', '(', ' ', '-'];
            let truncated = cleaned.substring(0, 50);
            
            for (const breakPoint of breakPoints) {
                const lastIndex = truncated.lastIndexOf(breakPoint);
                if (lastIndex > 15) {
                    truncated = truncated.substring(0, lastIndex);
                    break;
                }
            }
            cleaned = truncated;
        }
        
        return cleaned || '未命名图集';
    }

    chunkArray(array, size) {
        const chunks = [];
        for (let i = 0; i < array.length; i += size) {
            chunks.push(array.slice(i, i + size));
        }
        return chunks;
    }

    delay(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    showStats() {
        const elapsed = Math.round((Date.now() - this.stats.startTime) / 1000);
        const hours = Math.floor(elapsed / 3600);
        const minutes = Math.floor((elapsed % 3600) / 60);
        const seconds = elapsed % 60;
        
        const avgTimePerArticle = this.stats.totalArticles > 0 ? (elapsed / this.stats.totalArticles).toFixed(1) : 0;
        const avgImagesPerArticle = this.stats.totalArticles > 0 ? (this.stats.totalImages / this.stats.totalArticles).toFixed(1) : 0;
        
        console.log('\n💃 美女分类爬取完成！');
        console.log('='.repeat(60));
        console.log(`⏱️  总用时: ${hours}h ${minutes}m ${seconds}s`);
        console.log(`📁 成功图集: ${this.stats.totalArticles} 个`);
        console.log(`⏭️  跳过图集: ${this.stats.skippedArticles} 个 (已存在)`);
        console.log(`❌ 失败图集: ${this.stats.failedDownloads} 个`);
        console.log(`🖼️  总图片: ${this.stats.totalImages} 张`);
        console.log(`⚡ 平均速度: ${avgTimePerArticle}s/图集 | ${avgImagesPerArticle}张/图集`);
        console.log(`📂 保存位置: ${this.outputDir}`);
        console.log('='.repeat(60));
    }

    async run() {
        console.log('💃 Hyun Gallery 美女分类专用爬虫');
        console.log('=================================');
        
        try {
            await this.init();
            await this.scrapeBeautyCategory();
            this.showStats();
            
        } catch (error) {
            console.error('❌ 运行出错:', error.message);
        }
    }
}

// 主程序
async function main() {
    const scraper = new BeautyOnlyScraper();
    await scraper.run();
}

main().catch(console.error);
